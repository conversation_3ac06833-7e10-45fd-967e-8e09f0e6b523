export const getTargetSlide = (content, cursorLine) => {
  var lines = content.split('\n');

  var line = "";
  var slide = 0;
  var subSlide = 0;
  var page = 0;
  let prevSeperatorAt = -1;

  for (let i = 0; i < cursorLine; i++) {
    line = lines[i];

    if (/^--- *$/.test(line)) {
      prevSeperatorAt = i;
      slide = slide + 1;
      subSlide = 0;
      page++;
    } else if (/^-- *$/.test(line)) {
      prevSeperatorAt = i;
      subSlide = subSlide + 1;
      page++;
    }
  }

  const pages = content.split(/\n---? *\n/);

  var slideNumber = {
    "h": slide,
    "v": subSlide,
    "currentPage": pages[page] || '',
    "currentPageToCurrentLine": {
      content: lines.slice(prevSeperatorAt + 1, cursorLine).join('\n'),
      start: prevSeperatorAt === -1 ? 0 : lines.slice(0, prevSeperatorAt + 1).join('\n').length + 1,
      end: lines.slice(0, cursorLine).join('\n').length,
    },
    lines,
    "totalPages": {
      h: (content.match(/\n--- *\n/g) || []).length,
      v: (content.match(/\n-- *\n/g) || []).length
    },
    hasNotes: /\nNotes?:/.test(content)
  };

  return slideNumber;
};

export const getLineNumberFromSlideIndex = (content, indexh, indexv) => {
  const lines = content.split('\n');
  let currentH = 0;
  let currentV = 0;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];

    if (/^--- *$/.test(line)) {
      // 遇到水平分隔符
      currentH++;
      currentV = 0;

      // 如果找到了目标幻灯片，返回下一行的行号（分隔符后的第一行）
      if (currentH === indexh && currentV === indexv) {
        return i + 2; // +2 因为要跳过分隔符行，并且行号从1开始
      }
    } else if (/^-- *$/.test(line)) {
      // 遇到垂直分隔符
      currentV++;

      // 如果找到了目标幻灯片，返回下一行的行号（分隔符后的第一行）
      if (currentH === indexh && currentV === indexv) {
        return i + 2; // +2 因为要跳过分隔符行，并且行号从1开始
      }
    }
  }

  // 如果是第一张幻灯片 (0,0)，返回第1行
  if (indexh === 0 && indexv === 0) {
    return 1;
  }

  // 如果没找到，返回第1行
  return 1;
};

export const  sendMessageToPreview = (server_host, method, params, args) => {
  document.getElementById('slides-frame').contentWindow.postMessage(JSON.stringify({
    method,
    params,
    args
  }), server_host);
};
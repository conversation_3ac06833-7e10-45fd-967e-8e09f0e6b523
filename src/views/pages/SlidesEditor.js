/* @flow */

import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { useLocation, useHistory } from 'react-router-dom'
import { AI_ASSISTANT_DIALOG, DOC_ACTIONS, PRIVATE_DOCS_ACTIONS, SHARED_DOCS_ACTIONS, SHOW_APP_LIST, SLIDE_EDITOR_MESSAGE, SUB_DOCS_ACTIONS, WORKSPACE_DOCS_ACTIONS } from 'src/constants/actionTypes';

// import SpinnerAndToast, { showMessage } from './SpinnerAndToast';
import CmEditor from 'src/components/slides-editor/CmEditor';
import SlidesPreview from 'src/components/slides-editor/preview';
import { get_server_host } from 'src/utils/serverAPIUtil';
import { upsertDoc } from 'src/actions/ticketAction';
import { getTargetSlide, getLineNumberFromSlideIndex } from 'src/utils/SlidesUtil';
import { StatusBar } from 'src/components/slides-editor/StatusBar';
import { useIntl } from 'react-intl';
import { isMac } from '../../constants/constants';
import { sendMessageToPreview } from '../../utils/SlidesUtil';
import { getDoc } from '../../actions/ticketAction';

const SlidesEditor = () => {
  const dispatch = useDispatch();
  const intl = useIntl();
  const location = useLocation();
  const params = new Proxy(new URLSearchParams(location.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });
  const [hid, setHid] = useState();

  const docs = useSelector(state => state.docs);
  const [doc, setDoc] = useState();

  const container = useRef(null);
  const editorRef = useRef(null);

  const [server_host, setServer_host] = useState();
  const OSName = isMac ? 'mac' : 'win';
  const [viewerReady, setViewerReady] = useState(false);
  const [docSaved, setDocSaved] = useState(false);

  const [slides, setSlides] = useState({});
  const [message, setMessage] = useState();
  const [lastSavedContent, setLastSavedContent] = useState();

  useEffect(() => {
    get_server_host().then((value) => {
      setServer_host(value)
    });
  }, []);

  useEffect(() => {
    setHid(location.state && location.state.hid || params.hid);
  }, [location]);

  useEffect(() => {
    if (hid) {
      dispatch({ type: SHOW_APP_LIST, value: false });

      dispatch(getDoc({ hid }, null, null, 'editor'));
    }

    setLastSavedContent(docs?.byId[hid]?.markdown)
  }, [hid]);

  useEffect(() => {
    if(!hid || !docs) return;

    setDoc(docs.byId[hid]);
  }, [docs, hid])


  useEffect(() => {
    viewerReady && sendMessageToPreview(server_host, 'markdownUpdate', [doc?.markdown || 'Preview'])
  }, [doc?.hid, server_host, viewerReady])

  useEffect(() => {
    docSaved && viewerReady && sendMessageToPreview(server_host, 'markdownUpdate', [doc?.markdown || 'Preview'])
  }, [docSaved])

  useEffect(() => {
    if (slides.hasNotes) {
      sendMessageToPreview(server_host, 'reInitializeReveal', [true])
    }
  }, [slides.hasNotes])

  // const sendMessageToPreview = useCallback((method, params, args) => {
  //   document.getElementById('slides-frame').contentWindow.postMessage(JSON.stringify({
  //     method,
  //     params,
  //     args
  //   }), server_host);
  // }, [server_host]);

  const handleDocChange = useCallback((content, cursorLine) => {
    if (!doc) {
      return;
    }

    if (content != doc.markdown) {
      dispatch({
        type: DOC_ACTIONS.updated,
        item: {
          ...(doc || {}),
          markdown: content
        }
      })
      setMessage('save_shortcut');
    } else {
      setMessage('editor_slash_hint');
    }

    let currentSlides = getTargetSlide(content, cursorLine);
    setSlides(currentSlides)

    sendMessageToPreview(server_host, 'slide', undefined, [currentSlides.h, currentSlides.v]);

    if (currentSlides.totalPages.v === slides.totalPages?.v && currentSlides.totalPages.h === slides.totalPages?.h) {
      sendMessageToPreview(server_host, 'slideUpdate', [currentSlides.currentPage])
    } else {
      sendMessageToPreview(server_host, 'markdownUpdate', [content || 'Preview'])
    }

  }, [slides, doc])

  const saveDoc = useCallback((markdown, silient) => {
    if (markdown == lastSavedContent) return;

    dispatch(upsertDoc({ data: { doc: { hid, markdown } } }, (updatedDoc) => {
      setDocSaved(true);
      setMessage('doc_saved');
      setLastSavedContent(markdown)
    }, !silient, 'editor'))
  }, [lastSavedContent, hid])

  const handleSlideChange = useCallback((indexh, indexv) => {
    console.log('handle slide change..........', indexh, indexv)
    if (!doc?.markdown || !editorRef.current) return;

    const lineNumber = getLineNumberFromSlideIndex(doc.markdown, indexh, indexv);
    console.log('lineNumber..........', lineNumber)
    editorRef.current.scrollToLine(lineNumber);
  }, [doc?.markdown])

  return (
    <div
      style={{
        width: '100%', height: '100%', overflow: 'hidden', display: 'flex', flexDirection: 'row',
      }}
      ref={container}

      onKeyDown={(event) => {
        if ((OSName === 'win' && event.ctrlKey || OSName === 'mac' && event.metaKey) && (event.key === 's' || event.key === 'S')) {
          event.preventDefault();
          event.stopPropagation();

          saveDoc(doc.markdown);
          return;
        }
      }}
    >
      <div style={{ display: 'flex', flexDirection: 'column', flex: 2 }}>
        <CmEditor
          ref={editorRef}
          onChange={handleDocChange}
          onBlur={() => doc?.markdown && saveDoc(doc.markdown, true)}
          initialDoc={doc}
        />
        <StatusBar slides={slides} message={message} />
      </div>
      {
        doc?.markdown &&
      <div
        style={{
          height: '100%',
          overflowY: 'auto',
          overflowX: 'hidden',
          // boxShadow: '0px 0px 8px rgba(0, 0, 0, 0.3)',
          borderLeft: '1px solid rgb(221, 221, 221)',
          flex: 3,
        }}
      >
        <SlidesPreview
          viewerReady={() => {
            setViewerReady(true);
          }}
          resetMarkdown={() => {
            sendMessageToPreview(server_host, 'markdownUpdate', [doc?.markdown || 'Preview'])
          }}
          onSlideChange={handleSlideChange}
          hid={hid}
        />
      </div>
      }
    </div>
  )
}

export default SlidesEditor;

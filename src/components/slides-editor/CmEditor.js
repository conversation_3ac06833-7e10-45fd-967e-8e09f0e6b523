import React, { use<PERSON><PERSON>back, useEffect, useState, useImperative<PERSON><PERSON>le, forwardRef } from 'react'
import useCodeMirror from './use-codemirror'
import { useLocation, useHistory } from 'react-router-dom'
import './editor.css'
import { CmdMenu } from './cmdMenu'
import { keymap, EditorView } from '@codemirror/view'
// import AIModal from '../components/AIModal'
import { BalloonMenu } from './balloonMenu'
import { cursorLineDown, cursorLineUp, redo, undo } from '@codemirror/commands'
import { openSearchPanel } from "@codemirror/search"
import SpinnerAndToast from '../SpinnerAndToast'
import { AI_ASSISTANT_DIALOG, CHART_GENERATOR_DIALOG, CONFIRM_DIALOG, IMAGE_UPLOAD_DIALOG, LINK_INPUT_DIALOG, POLL_GENERATOR_DIALOG, TABLE_BUILDER_DIALOG } from 'src/constants/actionTypes'
import { useDispatch, useSelector } from 'react-redux'
import { PollGenerator } from './PollGenerator'
import { useIntl } from 'react-intl'
import { ChartGenerator } from './ChartGenerator'
import { LinkInputModal } from '../common/LinkInputModal'
import { getTargetSlide } from 'src/utils/SlidesUtil'
import { TableBuilderModal } from './TableBuilderModal'

const CmEditor = forwardRef((props, ref) => {
  const { onChange, initialDoc, onScroll, onKeyDown, scrollScale, onBlur } = props
  const [popupMenuState, setPopupMenuState] = useState({ itemTargeted: 0 })
  const [balloonMenuState, setBalloonMenuState] = useState({})
  const [editorState, setEditorState] = useState();
  const dispatch = useDispatch();
  const intl = useIntl();
  const location = useLocation();
  const history = useHistory();
  const params = new Proxy(new URLSearchParams(location.search), {
    get: (searchParams, prop) => searchParams.get(prop),
  });

  const aiState = useSelector(state => state.uiState.aiDialog) || {};
  useEffect(() => {
    const { response, trigger, doing, operation, visible, caller } = aiState;
    if (visible || caller != 'slides' || !response) {
      return;
    }

    let content = response.content;

    if (['speechscript', 'speakernotes'].includes(doing?.action)) {
      content = 'Notes: ' + content;
    } else if (doing?.action === 'title') {
      if (!content.startsWith('#')) {
        content = '## ' + content;
      }
    }

    content = content + "\n"

    let selection = editorState.selection.ranges[0];

    if (operation === 'insertBelow') {
      content = "\n" + content;
      let focus = selection.to;
      if (['title', 'keypoints'].includes(doing.action)) {
        let targetSlide = getTargetSlide(editorState.doc.toString(), editorState.doc.lineAt(focus).number)

        focus = targetSlide.currentPageToCurrentLine.start;
      }

      if (!!editorState.doc.lineAt(focus).text?.trim()) {
        content = content + '\n';
      }

      editorView.dispatch({
        changes: { from: focus, to: focus, insert: content },
        selection: { anchor: focus, head: focus + content.length }
      })
    } else if (operation === 'replace') {
      let { from, to } = selection;

      if (doing.action === 'keypoints') {
        let targetSlide = getTargetSlide(editorState.doc.toString(), editorState.doc.lineAt(to).number)
        from = targetSlide.currentPageToCurrentLine.start;
        const notesMatch = targetSlide.currentPage?.match(/note[s]?:/i);
        if (notesMatch) {
          to = targetSlide.currentPageToCurrentLine.start + notesMatch.index;
        }
      }
      content = content + '\n';

      editorView.dispatch({
        changes: { from, to, insert: content },
        selection: { anchor: from, head: from + content.length }
      })
    }

    dispatch({
      type: AI_ASSISTANT_DIALOG,
      value: {
        visible: false
      }
    })
  }, [aiState])

  const tableBuilderState = useSelector(state => state.uiState.tableBuilderDialog) || {};
  useEffect(() => {
    const { data } = tableBuilderState;
    if (tableBuilderState.visible || !data) {
      return;
    }

    const { columns, rows } = data;
    let markdownTable = '|';

    // 生成表头
    for (let i = 0; i < columns; i++) {
      markdownTable += '   |';
    }
    markdownTable += '\n|';

    // 生成表头分隔线
    for (let i = 0; i < columns; i++) {
      markdownTable += '---|';
    }
    markdownTable += '\n';

    // 生成空行
    for (let i = 0; i < rows; i++) {
      markdownTable += '|';
      for (let j = 0; j < columns; j++) {
        markdownTable += '   |';
      }
      markdownTable += '\n';
    }

    markdownTable && insert(-1, markdownTable, markdownTable.length - 1);


    dispatch({
      type: TABLE_BUILDER_DIALOG,
      value: {
        visible: false
      }
    })
  }, [tableBuilderState])

  const linkInputState = useSelector(state => state.uiState.linkInputDialog) || {};
  useEffect(() => {
    const { data } = linkInputState;
    if (linkInputState.visible || !data) {
      return;
    }

    let str;
    if (linkInputState.trigger === 'link') {
      str = `[${data.text}](${data.link})`;
    } else {
      str = `<iframe frameborder="0" style="width: 100%; height: 80vh" src="${data.link}"></iframe>`;
    }

    if (str) {
      const selection = editorState.selection.ranges[0];
      editorView.dispatch({
        changes: { ...selection, insert: str },
      })
    }

    dispatch({
      type: LINK_INPUT_DIALOG,
      value: {
        visible: false
      }
    })
  }, [linkInputState])

  const imageUploadState = useSelector(state => state.uiState.imageUploadDialog) || {};
  useEffect(() => {
    const { image } = imageUploadState;
    if (imageUploadState.visible || imageUploadState.trigger != 'slides_editor' || !image?.link) {
      return;
    }

    insert(-1, `![${image.altText || ''}](${image.link})`, 1);

    dispatch({
      type: IMAGE_UPLOAD_DIALOG,
      value: {
        visible: false
      }
    })
  }, [imageUploadState])

  const chartGeneratorState = useSelector(state => state.uiState.chartGeneratorDialog) || {};
  useEffect(() => {
    const { data } = chartGeneratorState;
    if (chartGeneratorState.visible || !data) {
      return;
    }

    var csv_data;

    if (data.source === 'url') {
      csv_data = data.url;
    } else {
      csv_data = data.csv;
      csv_data = csv_data.split("\n").filter(row => !!row?.trim());
    }

    insert(-1, makeChartSnippet(data.chart, data.source, csv_data), 0);

    dispatch({
      type: CHART_GENERATOR_DIALOG,
      value: {
        visible: false
      }
    })
  }, [chartGeneratorState])

  const pollGeneratorState = useSelector(state => state.uiState.pollGeneratorDialog) || {};
  useEffect(() => {
    const { data } = pollGeneratorState;
    if (pollGeneratorState.visible || !data) {
      return;
    }

    const options = data.options.filter(o => !!o?.trim());
    const id = new Date().getTime().toString();
    const chart_data = [];
    let row = '';
    options.forEach(option => {
      if (/^[\u4e00-\u9fa5]/.test(option)) {
        option = option.replace(',', '，');
      } else {
        option = option.replace(',', '_');
      }
      row += `, ${option}`
    });

    chart_data.push(row);

    row = '';
    options.forEach(option => {
      row += `, 0`
    });
    chart_data.push(row);


    let pollStr = `${data.desc}\n`
    pollStr += `<div class="poll" data-poll="${id}">\n`;

    options.forEach((option, index) => {
      pollStr += `  <button data-value="${index}">${option}</button>\n  <br>\n`;
    })

    pollStr += '</div>\n' +
      `<p style="font-size:24px;color:gray;">${intl.formatMessage({ id: "poll_responses" })}: <span class="voters" data-poll="${id}">0</span>` +
      '\n</p>' +
      '\n\n---\n\n' +
      '## ' + intl.formatMessage({ id: "poll_result" }) + '\n' +
      makeChartSnippet(data.chart, 'csv', chart_data, [{ name: 'data-poll', value: id }]);

    insert(-1, pollStr, 0);

    dispatch({
      type: POLL_GENERATOR_DIALOG,
      value: {
        visible: false
      }
    })
  }, [pollGeneratorState])

  const makeChartSnippet = useCallback((type, data_type, data, attrs) => {
    const attrs_str = attrs?.map(attr => {
      return `${attr.name}="${attr.value}"`;
    });

    let chart_str = '<div style="height:480px">\n' +
      `  <canvas data-chart="${type}" ${attrs_str || ''} ${data_type == 'url' ? "data-chart-src='" + data + "'" : ''}>\n` +
      '    ' + (data_type !== 'url' ? data.join('\n    ') : '') +
      "\n  </canvas>" +
      "\n</div>";

    return chart_str;
  }, []);

  useEffect(() => {
    if (!editorState) return;

    const cursorAt = editorState.selection.ranges[0].from;
    const line = editorState.doc.lineAt(cursorAt);

    if (!popupMenuState?.isVisible) {
      let content = editorState.doc.toString();
      onChange(content, line.number);
      // setLastContext({content, cursorAt})

      // if (initialDoc?.content !== editorState.doc.toString()) {
      //   onChange();
      // }
    } else {
      const columnIndex = cursorAt - line.from;
      let textBefore = line.text.substring(0, columnIndex);
      const slashAt = textBefore.lastIndexOf('/');

      if (slashAt === -1) {
        return setPopupMenuState({
          isVisible: false
        })
      }

      let searchText = line.text.substring(slashAt + 1, columnIndex);
      let searchEnd = columnIndex;
      const textAfterCursor = line.text.substring(columnIndex);

      if (textAfterCursor?.trim()) {
        searchText += textAfterCursor.split(' ')[0];
        searchEnd = slashAt + 1 + searchText.length;
      }

      setPopupMenuState({
        ...popupMenuState,
        lineInfo: {
          ...line,
          textBefore: '',
          textBehind: line.text.substring(searchEnd),
          searchText: searchText,
          searchStart: slashAt + 1,
          searchEnd,
          textBefore: textBefore.substring(0, slashAt)
        }
      })
    }
  }, [editorState])

  const handleChange = useCallback(
    (state) => {
      setEditorState(state)
    }, [])

  const getCursorXY = useCallback(({ view, lineElement, lineText, columnIndex, popupMaxHeight }) => {
    const {
      offsetLeft: inputX,
      offsetTop: inputY,
    } = lineElement;

    const div = document.createElement('div');
    const copyStyle = getComputedStyle(lineElement);
    for (const prop of copyStyle) {
      div.style[prop] = copyStyle[prop];
    }

    const textContent = lineText.substr(0, columnIndex);
    const span1 = document.createElement('span');
    span1.textContent = textContent;

    if (lineElement.tagName === 'TEXTAREA') div.style.height = 'auto';
    if (lineElement.tagName === 'INPUT') div.style.width = 'auto';
    const span2 = document.createElement('span');
    span2.textContent = lineText.substr(columnIndex) || '.';

    div.appendChild(span1);
    div.appendChild(span2);
    document.getElementById('editor-wrapper').appendChild(div);
    const { offsetHeight: span1Height } = span1;
    const { offsetLeft: spanX, offsetHeight: span2Height } = span2;
    const { offsetHeight: divHeight } = div;
    document.getElementById('editor-wrapper').removeChild(div);

    let y;
    y = inputY - view.scrollDOM.scrollTop + span1Height;
    let popBelow = true;
    if (y + popupMaxHeight > view.scrollDOM.getBoundingClientRect().height) {
      // y = view.scrollDOM.getBoundingClientRect().height - inputY + view.scrollDOM.scrollTop - (divHeight - span2Height);
      popBelow = false;
    }
    // console.log('position.......', { inputY, span1Height, divHeight, span2Height, divHeight, screenHeight: view.scrollDOM.getBoundingClientRect().height, y2: view.scrollDOM.getBoundingClientRect().height - inputY + view.scrollDOM.scrollTop - (divHeight - span2Height), scrollTop: view.scrollDOM.scrollTop })
    return {
      x: inputX + spanX,
      y,
      top: view.scrollDOM.getBoundingClientRect().height - inputY + view.scrollDOM.scrollTop - (divHeight - span2Height),
      popBelow
    };
  }, []);

  const getCursorElement = () => {
    const selection = window.getSelection();
    let lineElement;
    if (selection.rangeCount > 0) {
      const range = selection.getRangeAt(0);
      lineElement = range.startContainer.nodeType === 1 ? range.startContainer : range.startContainer.parentNode;
    }

    return lineElement;
  }

  const handleInut = useCallback((view, from, to, text) => {
    // console.log('input handler........', { view, from, to, text }, view.state.doc.lineAt(from), view.contentDOM.children)

    if (text != '/') {
      return;
    }

    const line = view.state.doc.lineAt(to);
    const columnIndex = to - line.from;

    if (line.text.charAt(columnIndex)?.trim()) {
      return;
    }

    const lineText = line.text.substring(0, columnIndex) + text + line.text.substring(columnIndex);
    const lineElement = getCursorElement();
    // const lineElement = Array.from(view.contentDOM.children)[line.number - 1];

    const xy = getCursorXY({ view, lineElement, lineText, columnIndex, popupMaxHeight: 310 });
    setPopupMenuState({
      ...popupMenuState,
      isVisible: true,
      lineElement,
      position: {
        left: xy.x,
        top: xy.popBelow ? xy.y : undefined,
        bottom: xy.popBelow ? undefined : xy.top,
      },
      lineInfo: {
        ...line,
        lineText
      }
    })
  }, [])

  const [refContainer, editorView] = useCodeMirror({
    initialDoc: initialDoc,
    onChange: handleChange,
    onInput: handleInut,
    onClick: () => {
      setPopupMenuState({ isVisible: false });
    },
    editorReadyCallback: (view) => {
      if (!view || !initialDoc || !location.state?.withAI) {
        return;
      }

      view.dispatch({
        selection: {
          anchor: view.state.doc.toString().length,
          head: view.state.doc.toString().length
        },
      });

      dispatch({
        type: AI_ASSISTANT_DIALOG,
        value: {
          caller: 'slides',
          visible: true,
          trigger: 'entrance',
          objType: 'slides',
          selectedText: initialDoc.title,
          anchorEl: Array.from(view.contentDOM.children)[0]
        }
      });

      history.replace({
        ...location,
        state: {
          ...location.state,
          withAI: false
        },
      });
    }
  })

  useEffect(() => {
    if (editorView) {
      editorView.scrollDOM.onscroll = (event) => {
        onScroll(event, editorView.scrollDOM);
      }
    }
  }, [onScroll])

  useEffect(() => {
    if (editorView) {
      editorView.contentDOM?.addEventListener('blur', onBlur)
    } else {
      // loading editor
    }

    return () => editorView?.contentDOM?.removeEventListener('blur', onBlur)
  }, [editorView, onBlur])

  useEffect(() => {
    if (!editorView?.scrollDOM || scrollScale < 0) return;

    editorView.scrollDOM.scrollTop = scrollScale * (editorView.scrollDOM.scrollHeight - editorView.scrollDOM.getBoundingClientRect().height) / 100
  }, [scrollScale])

  const insert = useCallback((at, text, back) => {
    if (!editorView?.state) {
      return;
    }

    if (at === -1) {
      at = editorView.state.selection.ranges[0]?.from
    }

    editorView.dispatch({
      changes: { from: at, insert: text },
      selection: { anchor: at + text.length - (back ? back : 0) }
    })
  }, [editorView]);

  const toggleBalloonMenu = () => {

    const selection = window.getSelection();

    if (!selection.isCollapsed) {
      const lineElementFrom = selection.anchorNode.nodeType === 1 ? selection.anchorNode : selection.anchorNode.parentNode;
      const lineElementTo = selection.focusNode.nodeType === 1 ? selection.focusNode : selection.focusNode.parentNode;

      let range = editorView.state.selection?.ranges[0];
      const lineFrom = editorView.state.doc.lineAt(range.from);
      const columnIndexFrom = range.from - lineFrom.from;
      const lineTextFrom = lineFrom.text;

      const lineTo = editorView.state.doc.lineAt(range.to);
      const columnIndexTo = range.to - lineTo.from;
      const lineTextTo = lineTo.text;

      const xy1 = getCursorXY({ view: editorView, lineElement: lineElementFrom, lineText: lineTextFrom, columnIndex: columnIndexFrom, popupMaxHeight: 100 });
      const xy2 = getCursorXY({ view: editorView, lineElement: lineElementTo, lineText: lineTextTo, columnIndex: columnIndexTo, popupMaxHeight: 100 });
      // console.log('xy..............', xy1, xy2, lineTo, range.to)
      setBalloonMenuState({
        ...balloonMenuState,
        isVisible: true,
        lineElement: xy2.popBelow ? lineElementTo : lineElementFrom,
        position: {
          left: xy1.x,
          top: xy2.popBelow ? xy2.y : undefined,
          bottom: xy2.popBelow ? undefined : xy1.top,
        }
      });
    } else {
      setBalloonMenuState({ isVisible: false });
    }
  }

  useImperativeHandle(ref, () => ({
    scrollToLine: (lineNumber) => {
      console.log('scroll to line..........', lineNumber, editorView)
      if (!editorView) return;

      const line = editorView.state.doc.line(Math.min(lineNumber, editorView.state.doc.lines));
      console.log('line..........', line)
      editorView.dispatch({
        effects: EditorView.scrollIntoView(line.from, { y: 'start' })
      });
    }
  }), [editorView]);

  return <div
    className='editor-wrapper'
    id='editor-wrapper'
    ref={refContainer}
    onMouseUp={toggleBalloonMenu}
    onKeyDown={(event) => {
      if (onKeyDown) {
        onKeyDown(event);
      }

      if (!popupMenuState?.isVisible && !event.shiftKey) {
        if (event.key === 'ArrowUp') {
          cursorLineUp(editorView)
        } else if (event.key === 'ArrowDown') {
          cursorLineDown(editorView)
        }
      }

      toggleBalloonMenu();
    }}
  >
    <CmdMenu
      state={popupMenuState}
      editorView={editorView}
      insert={insert}
      onClose={() => setPopupMenuState({ isVisible: false })}
      hid={initialDoc?.hid}
    />
    <BalloonMenu
      state={balloonMenuState}
      editorView={editorView}
      insert={insert}
      onClose={() => {
        setBalloonMenuState({ isVisible: false });
      }}
      hid={initialDoc?.hid}
    />
    {/* <AIModal
      editorView={editorView}
    /> */}
    {/* <CONFIRM_DIALOG /> */}
    <LinkInputModal />
    <PollGenerator />
    <ChartGenerator />
    <TableBuilderModal />
    <SpinnerAndToast />
  </div>
});

export default CmEditor;
